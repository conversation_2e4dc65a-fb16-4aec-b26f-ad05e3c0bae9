import { Component, OnInit, Input, Output, EventEmitter, OnDestroy } from '@angular/core';
import { AuthService } from '../../services/auth.service';
import { Router } from '@angular/router';
import { MemberService } from '../../services/member.service';
import { ProfileImageService, ProfileImageState } from '../../services/profile-image.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'],
  standalone: false
})
export class SidebarComponent implements OnInit, OnDestroy {
@Input() collapsed: boolean = false;
@Input() isDarkMode: boolean = false;
@Output() toggleSidebar = new EventEmitter<void>();
@Output() toggleDarkMode = new EventEmitter<void>();

  isOwner: boolean = false;
  isAdmin: boolean = false;
  isMember: boolean = false;
  hasMemberQRAccess: boolean = false;

  // Menu sections
  customerMenuOpen: boolean = false;
  eMoneyMenuOpen: boolean = false;
  trainingMenuOpen: boolean = false;
  licenseMenuOpen: boolean = false;
  gymMenuOpen: boolean = false;
  systemMenuOpen: boolean = false;

  // Hover functionality
  isHovered: boolean = false;
  hoverTimeout: any;
  private readonly HOVER_DELAY = 300; // ms

  // Profil fotoğrafı yönetimi
  profileImageState: ProfileImageState = {
    imageUrl: null,
    hasImage: false,
    isLoading: false,
    lastUpdated: null
  };
  private profileImageSubscription: Subscription = new Subscription();

  constructor(
    private authService: AuthService,
    private router: Router,
    private memberService: MemberService,
    private profileImageService: ProfileImageService
  ) {
    // Subscribe to user changes
    this.authService.currentUser.subscribe(user => {
      if (user) {
        this.isOwner = this.authService.hasRole('owner');
        this.isAdmin = this.authService.hasRole('admin');
        this.isMember = this.authService.hasRole('member');

        // Eğer member rolüne sahipse, QR kodu erişimi kontrolü yap
        if (this.isMember) {
          this.checkMemberQRAccess();
        } else {
          this.hasMemberQRAccess = false;
        }
      } else {
        this.isOwner = false;
        this.isAdmin = false;
        this.isMember = false;
        this.hasMemberQRAccess = false;
      }
    });
  }

  ngOnInit() {
    // Initial check
    this.isOwner = this.authService.hasRole('owner');
    this.isAdmin = this.authService.hasRole('admin');
    this.isMember = this.authService.hasRole('member');

    // Eğer member rolüne sahipse, QR kodu erişimi kontrolü yap
    if (this.isMember) {
      this.checkMemberQRAccess();
    }

    // Profil fotoğrafı state'ini dinle
    this.profileImageSubscription = this.profileImageService.profileImage$.subscribe(
      (state: ProfileImageState) => {
        this.profileImageState = state;
      }
    );

    // Window resize event listener ekle
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', this.onWindowResize.bind(this));
    }
  }

  ngOnDestroy() {
    // Subscription'ları temizle
    if (this.profileImageSubscription) {
      this.profileImageSubscription.unsubscribe();
    }
  }

  // Profil fotoğrafı yükleme hatası
  onProfileImageError(): void {
    // ProfileImageService'e hata durumunu bildir
    this.profileImageService.clearProfileImage();
  }

  // Üyenin QR koduna erişim hakkı olup olmadığını kontrol et
  checkMemberQRAccess() {
    if (!this.isMember) return;

    // Kullanıcı member rolüne sahipse, QR butonunu göster
    // API çağrısı yapmadan direkt olarak erişim veriyoruz
    // Gerçek kontrol my-qr component içinde yapılacak
    this.hasMemberQRAccess = true;
  }

  // Toggle sidebar
  onToggleSidebar() {
    this.toggleSidebar.emit();
  }

  // Toggle dark mode
  onToggleDarkMode() {
    this.toggleDarkMode.emit();
  }

  // Toggle menu sections
  toggleMenuSection(section: string) {
    if (!this.collapsed) {
      switch(section) {
        case 'customer':
          this.customerMenuOpen = !this.customerMenuOpen;
          break;
        case 'eMoney':
          this.eMoneyMenuOpen = !this.eMoneyMenuOpen;
          break;
        case 'training':
          this.trainingMenuOpen = !this.trainingMenuOpen;
          break;
        case 'license':
          this.licenseMenuOpen = !this.licenseMenuOpen;
          break;
        case 'gym':
          this.gymMenuOpen = !this.gymMenuOpen;
          break;
        case 'system':
          this.systemMenuOpen = !this.systemMenuOpen;
          break;
      }
    }
  }

  logout(event: Event) {
    event.preventDefault();
    this.authService.logout();
  }

  isAuthenticated(): boolean {
    return this.authService.isAuthenticated();
  }

  // Hover functionality methods
  onSidebarMouseEnter() {
    if (this.collapsed && window.innerWidth >= 992) { // Sadece desktop'ta çalışsın
      this.clearHoverTimeout();
      this.hoverTimeout = setTimeout(() => {
        this.isHovered = true;
      }, this.HOVER_DELAY);
    }
  }

  onSidebarMouseLeave() {
    if (this.collapsed && window.innerWidth >= 992) { // Sadece desktop'ta çalışsın
      this.clearHoverTimeout();
      this.hoverTimeout = setTimeout(() => {
        this.isHovered = false;
      }, this.HOVER_DELAY);
    }
  }

  private clearHoverTimeout() {
    if (this.hoverTimeout) {
      clearTimeout(this.hoverTimeout);
      this.hoverTimeout = null;
    }
  }

  private onWindowResize() {
    // Ekran boyutu değiştiğinde hover durumunu sıfırla
    if (typeof window !== 'undefined' && window.innerWidth < 992) {
      this.isHovered = false;
      this.clearHoverTimeout();
    }
  }

  ngOnDestroy() {
    this.clearHoverTimeout();

    // Window resize event listener'ı temizle
    if (typeof window !== 'undefined') {
      window.removeEventListener('resize', this.onWindowResize.bind(this));
    }
  }
}
